"""
Calculation Package

This package contains mathematical calculation functions, statistical operations,
data processing methods, and computational algorithms extracted from the
general_logic_helper module.

The package is organized into modules by calculation type:
- fuel: Fuel consumption and flow calculations
- time: Time and date calculations
- statistics: Statistical operations and averaging calculations
- flow: Flow and mass calculations
- charts: Chart-related mathematical operations
- weather: Weather calculations and Beaufort scale operations
"""

from .fuel import FuelCalculations
from .time import TimeCalculations
from .statistics import StatisticalCalculations
from .flow import FlowCalculations
from .charts import ChartCalculations
from .weather import WeatherCalculations

__all__ = [
    "FuelCalculations",
    "TimeCalculations",
    "StatisticalCalculations",
    "FlowCalculations",
    "ChartCalculations",
    "WeatherCalculations"
]
