from fastapi import FastAPI, Depends
from fastapi.responses import ORJSONResponse

from auth import AuthHandler
from app.config.settings import settings
from app.utils.errors import register_error_handlers, register_exception_middleware
from app.modules.hull_performance.hull_performance_router import router as hull_performance_router
from app.modules.user.user_router import router as user_router
from app.modules.owner.owner_router import router as owner_router
from app.modules.vessel.vessel_router import router as vessel_router
from app.modules.home.home_router import router as home_router
from app.modules.efficiency.efficiency_router import router as efficiency_router
from app.modules.anomaly_detection.anomaly_detection_router import router as anomaly_detection_router
from app.modules.mrv.mrv_router import router as mrv_router
from app.modules.data_analytics.data_analytics_router import router as data_analytics_router
from app.modules.cii.cii_router import router as cii_router
from app.modules.export.export_router import router as export_router
from test.async_test.async_test_router import router as async_test_router


def create_api() -> FastAPI:
    auth_handler = AuthHandler(settings.jwt_secret)

    api = FastAPI(
        title="MyData Backend",
        version="1.0.0",
        description="FastAPI Backend",
        dependencies=[Depends(auth_handler)],
        default_response_class=ORJSONResponse
    )

    # register all your routers
    for router in (
        hull_performance_router,
        user_router,
        owner_router,
        vessel_router,
        home_router,
        efficiency_router,
        anomaly_detection_router,
        mrv_router,
        data_analytics_router,
        cii_router,
        export_router,
        async_test_router,
    ):
        api.include_router(router)

    register_exception_middleware(api)
    register_error_handlers(api)
    return api


app = create_api()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "run:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level,
        access_log=settings.access_log,
    )
