"""
Time Calculation Module

Contains time and date calculation methods.
"""

from datetime import datetime


class TimeCalculations:
    """Class containing time-related calculation methods."""
    
    @staticmethod
    def seconds_to_hours_minutes(seconds):
        """
        Convert seconds to hours and minutes.
        
        Args:
            seconds: Number of seconds
            
        Returns:
            Tuple of (hours, minutes)
        """
        hours = seconds // 3600  # 1 hour = 3600 seconds
        minutes = (seconds % 3600) // 60  # 1 minute = 60 seconds
        return hours, minutes

    @staticmethod
    def diff_in_days(from_date, to_date):
        """
        Calculate difference in days between two dates.
        
        Args:
            from_date: Start date (string or datetime)
            to_date: End date (string or datetime)
            
        Returns:
            Number of days difference
        """
        if from_date is not None and to_date is not None:
            if isinstance(from_date, str):
                from_date = datetime.strptime(from_date, "%Y-%m-%d %H:%M:%S")
            if isinstance(to_date, str):
                to_date = datetime.strptime(to_date, "%Y-%m-%d %H:%M:%S")
            diff_in_time = from_date - to_date
            return diff_in_time.days
        return None

    @staticmethod
    def validate_timestamp(timestamp):
        """
        Validate and convert timestamp to datetime object.
        
        Args:
            timestamp: Timestamp as string or datetime
            
        Returns:
            Datetime object or None
        """
        ts = None
        if isinstance(timestamp, datetime):
            ts = timestamp
        elif isinstance(timestamp, str):
            try:
                # Attempt to parse as an ISO 8601 formatted string
                ts = datetime.fromisoformat(timestamp)
            except ValueError:
                # Handle the case where the string is not ISO 8601
                ts = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S")
        return ts

    @staticmethod
    def convert_epoch_to_date_UTC(vessel_ais_data):
        """
        Convert epoch timestamps to UTC dates and calculate percentage.

        Args:
            vessel_ais_data: Dictionary containing AIS data with epoch timestamps

        Returns:
            Percentage of journey completed
        """
        percentage = 0
        if vessel_ais_data is not None:
            atd_epoch = vessel_ais_data.get("atd_epoch")
            eta_epoch = vessel_ais_data.get("eta_epoch")
            if (
                atd_epoch is not None
                and atd_epoch != 0
                and eta_epoch is not None
                and eta_epoch != 0
            ):
                start_date = datetime.fromtimestamp(atd_epoch)
                end_date = datetime.fromtimestamp(eta_epoch)
                percentage = (
                    (datetime.now() - start_date) / (end_date - start_date)
                ) * 100

                if percentage > 100:
                    percentage = 100
                elif percentage < 0:
                    percentage = 0

        return percentage
