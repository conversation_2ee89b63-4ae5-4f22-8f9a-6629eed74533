"""
Fuel Calculation Module

Contains fuel consumption and flow calculation methods.
"""

import logging
from orjson import orjson


class FuelCalculations:
    """Class containing fuel-related calculation methods."""
    
    @staticmethod
    def calculate_fuel_consumption(data, vessel_imo, log_speed_reliable):
        """
        Calculate fuel consumption based on vessel data.

        Args:
            data: List of vessel measurement data
            vessel_imo: Vessel IMO number
            log_speed_reliable: Speed log reliability setting

        Returns:
            List containing on and off fuel consumption values
        """
        data_list = []
        key = "me_1_total_6"
        frugal_status = None
        start_fc = None
        on_value = 0
        off_value = 0
        index = 0
        speed_type = "gspd" if log_speed_reliable == "gps_speed" else "lspd"
        speed_boundary = 4
        if vessel_imo in [9525572]:  # Key Stream
            speed_boundary = 8.5
        try:
            for element in data:
                # Calculate total fuel consumption the new way
                if isinstance(element["flwt"], str) and element.get("flwt") != "NULL":
                    element["flwt"] = orjson.loads(element["flwt"])
                if element.get("sstat") is True and (element.get(speed_type) >= speed_boundary):
                    if (
                        element["flwt"] is not None
                        and "me_1_total_6" in element["flwt"]
                        and element["flwt"][key] > 0.0
                        and vessel_imo != 9195391  # Ignore Vón Feeder
                        and vessel_imo != 9379478  # Ignore Ayse Ana
                    ):
                        fc_total_element = element["flwt"][key]
                        # Set Frugal Status
                        if frugal_status is None and start_fc is None:
                            frugal_status = element["fstat"]
                            start_fc = fc_total_element

                        if element["fstat"] != frugal_status or index == len(data) - 1:
                            # If Frugal Status changed from previous element, calculate FC
                            end_fc = fc_total_element

                            if start_fc is not None and end_fc is not None:
                                net_flow = start_fc - end_fc
                                if frugal_status >= 1:
                                    on_value += abs(net_flow)
                                else:
                                    off_value += abs(net_flow)
                                # Start Over
                                start_fc = fc_total_element
                                frugal_status = element["fstat"]
                    else:
                        # If switching from new to old method of collecting fuel consumption data
                        if start_fc is not None and key in data[index - 1]["flwt"]:
                            net_flow = start_fc - data[index - 1]["flwt"][key]
                            if frugal_status >= 1:
                                on_value += abs(net_flow)
                            else:
                                off_value += abs(net_flow)
                            # Reset
                            start_fc = None
                            frugal_status = None
                        # Calculate total fuel consumption the old way
                        if element["mefcm"] is not None and element["fstat"] is not None:
                            if element["fstat"] >= 1:
                                on_value += element["mefcm"] / 60 / 1000
                            else:
                                off_value += element["mefcm"] / 60 / 1000
                index += 1
            data_list.append(round(on_value, 2))
            data_list.append(round(off_value, 2))
        except Exception as e:
            logging.error(
                "Error while calculating total FC data for vessel underway %s in value_server: %s",
                vessel_imo,
                e,
            )
        return data_list

    @staticmethod
    def calculate_net_aux_flow(flow_a, flow_b, aux_add_groups):
        """
        Calculate net auxiliary flow.

        Args:
            flow_a: First auxiliary flow value
            flow_b: Second auxiliary flow value
            aux_add_groups: Whether to add or subtract flows

        Returns:
            Net auxiliary flow value
        """
        if flow_a is not None and flow_b is not None:
            try:
                if aux_add_groups is True:
                    net_flow = abs(float(flow_a) + float(flow_b))
                else:
                    net_flow = abs(float(flow_a) - float(flow_b))
                if net_flow < 0:
                    net_flow = 0
                return net_flow
            except (ValueError, TypeError) as e:
                logging.error("Error calculating net aux flow: %s", e)
                return 0
        return None

    @staticmethod
    def calculate_net_me_flow(flow_a, flow_b, me_fc_add_groups):
        """
        Calculate net main engine flow.

        Args:
            flow_a: First main engine flow value
            flow_b: Second main engine flow value
            me_fc_add_groups: Whether to add or subtract flows

        Returns:
            Net main engine flow value
        """
        if flow_a is not None and flow_b is not None:
            try:
                if me_fc_add_groups is True:
                    net_flow = abs(float(flow_a) + float(flow_b))
                else:
                    net_flow = abs(float(flow_a) - float(flow_b))
                if net_flow < 0:
                    net_flow = 0
                return net_flow
            except (ValueError, TypeError) as e:
                logging.error("Error calculating net ME flow: %s", e)
                return 0
        return None


