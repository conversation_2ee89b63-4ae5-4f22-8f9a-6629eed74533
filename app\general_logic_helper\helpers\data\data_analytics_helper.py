import bisect
import math

import numpy as np
import pandas as pd

from datetime import datetime
from app.general_logic_helper.utils.special_vessel_keys import aux_do_vessel_id_key_relation
from app.calculation.statistics import StatisticalCalculations
from app.calculation.time import TimeCalculations
from app.calculation.weather import WeatherCalculations


class DataAnalyticsHelper:
    # Use BEAUFORT_SCALE from WeatherCalculations
    BEAUFORT_SCALE = WeatherCalculations.BEAUFORT_SCALE

    @staticmethod
    def create_bf_dict():
        bf_dict = {key: {str(i): {} for i in range(13)} for key in ["lspd", "gspd"]}
        bf_dict["lspdMin"] = None
        bf_dict["gspdMin"] = None
        return bf_dict

    @staticmethod
    def init_data(
        data,
        from_date,
        to_date,
        log_speed_reliable,
        ais_data_df,
        weather_data_df,
        vessel_id=None,
        vessel_imo=None
    ):
        measurement_graphs_names = [
            "mefcm",
            "merpm",
            "spow",
            "lspd",
            "gspd",
            "boifcm",
            "auxfcm",
        ]

        report_graphs_names = ["lspdMefcm", "frugalStatusTime"]

        show_raw_data = False

        data_analytics_object = {}

        diff_in_days = TimeCalculations.diff_in_days(from_date, to_date)
        if diff_in_days <= 7:
            show_raw_data = True

        if vessel_id in aux_do_vessel_id_key_relation.keys():
            measurement_graphs_names.append("auxdo")

        measurement_data = pd.DataFrame(data["measurements"])

        # Calculate chunk size based on date range
        chunk_size = (
            25 if diff_in_days == 0 or diff_in_days is None else 25 * diff_in_days
        )

        calculated_data = StatisticalCalculations.calculate_averaged_columns(
            measurement_data, measurement_graphs_names, chunk_size
        )

        # Initialize measurement graphs dict
        measurement_graphs = {
            "show_raw_data": show_raw_data,
            "y_axis": {},
            "x_axis": {
                "calculated_categories": calculated_data["timestamp"],
                "max_timestamp": measurement_data["timestamp"]
                .max()
                .strftime("%Y-%m-%d %H:%M:%S"),
                "min_timestamp": measurement_data["timestamp"]
                .min()
                .strftime("%Y-%m-%d %H:%M:%S"),
            },
        }

        if show_raw_data:
            measurement_graphs["x_axis"]["raw_categories"] = [
                t.strftime("%Y-%m-%d %H:%M:%S")
                for t in measurement_data["timestamp"].tolist()
            ]

        # Calculate averaged data for all columns at once
        # Add data for each measurement graph
        for column in measurement_graphs_names:
            if column in measurement_data.columns:
                max_raw_y = measurement_data[column].max()
                max_calculated_y = max(calculated_data[column])

                measurement_graphs["y_axis"][column] = {
                    "calculated_data": calculated_data[column]
                    if column in calculated_data
                    else [],
                    "max_raw_y": round(float(max_raw_y), 1),
                    "max_calculated_y": round(float(max_calculated_y), 1),
                }

                if show_raw_data:
                    raw_data = measurement_data[column].tolist()
                    measurement_graphs["y_axis"][column]["raw_data"] = [
                        round(float(x), 1)
                        if x is not None and x != np.nan and x > 0
                        else 0
                        for x in raw_data
                    ]

        data_analytics_object["measurement_graphs"] = measurement_graphs

        for name in report_graphs_names:
            if name == "lspdMefcm":
                if len(data) > 0 and log_speed_reliable and not weather_data_df.empty:
                    (
                        data_analytics_object["lspdMefcm"],
                        data_analytics_object["lspdMefcmBestFit"],
                    ) = DataAnalyticsHelper.calculate_spd_mefcm_correlation(
                        data, log_speed_reliable, ais_data_df, weather_data_df
                    )
            elif name == "frugalStatusTime":
                data_analytics_object["frugalStatusTime"] = (
                    DataAnalyticsHelper.create_timeseries_for_frugal_status(
                        data, vessel_id, vessel_imo, log_speed_reliable
                    )
                )
        return data_analytics_object

    @staticmethod
    def preprocess_weather_data(weather_data):
        # Convert the list of dictionaries to a DataFrame
        df = pd.DataFrame(weather_data)
        # Convert the 'timestamp' column to datetime objects
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        # Calculate the Beaufort number for each row
        df["beaufort"] = df["wind_speed"].apply(WeatherCalculations.calculate_beaufort)
        # Sort the DataFrame by timestamp
        df.sort_values("timestamp", inplace=True)
        # Convert the DataFrame to a list of tuples
        weather_data_preprocessed = list(
            df[["timestamp", "beaufort"]].itertuples(index=False, name=None)
        )
        return weather_data_preprocessed

    @staticmethod
    def process_draft_data(draft_data, df):
        if draft_data is not None and not draft_data.empty:
            draft_df = draft_data.loc[:, ["draught", "timestamp", "last_update_utc"]]

            # Convert timestamps to datetime for accurate comparison
            draft_df["timestamp"] = pd.to_datetime(draft_df["last_update_utc"])

            # Convert all draught values in draft_df to numeric, setting errors='coerce' to handle non-numeric values
            draft_df["draught"] = pd.to_numeric(draft_df["draught"], errors="coerce")

            # Sort DataFrame by timestamp
            draft_df = draft_df.sort_values("timestamp")

            filtered_draft_df = draft_df.dropna(subset=["timestamp", "last_update_utc"])

            # Use merge_asof to find the closest timestamp in draft_df for each row in df
            if not df.empty and not filtered_draft_df.empty:
                merged_df = pd.merge_asof(
                    df,
                    filtered_draft_df,
                    on="timestamp",
                    direction="nearest",
                    tolerance=pd.Timedelta("10min"),
                )

                # Only update 'da' where the merge was successful (within 10 minutes)
                # merged_df['draught'] will be NaN if no close enough timestamp was found
                df.loc[(df["da"] == 0) & (merged_df["draught"].notna()), "da"] = (
                    merged_df["draught"]
                )
        return df

    @staticmethod
    def filter_timestamps(grouped_df, speed_col):
        filtered_df = pd.DataFrame()
        time_df = pd.DataFrame()
        for _, group in grouped_df:
            if len(group) < 10:
                continue
            group = group.sort_values("timestamp").reset_index(drop=True)
            time_diffs = (
                group["timestamp"].diff().dt.total_seconds().fillna(float("inf"))
            )
            close_mask = time_diffs <= 65
            group["sequence_id"] = (~close_mask).cumsum()
            group["seq_size"] = group.groupby("sequence_id")["timestamp"].transform(
                "size"
            )
            seq_group = group.loc[
                group["seq_size"] >= 10,
                [
                    "seq_size",
                    "beaufort",
                    speed_col,
                    "da",
                    "sequence_id",
                    "timestamp",
                    "mefcm",
                ],
            ]
            if not seq_group.empty:
                seq_group_time_df = WeatherCalculations.calculate_total_time(
                    seq_group, speed_col
                )
                time_df = time_df._append(seq_group_time_df, ignore_index=True)
                filtered_df = filtered_df._append(seq_group, ignore_index=True)

        return filtered_df, time_df

    @staticmethod
    def aggregate_filter_mefcm_cumsum(sub_df, speed_col, min_log_speed):
        sub_df = sub_df.copy()[
            (sub_df[speed_col] <= 30) & (sub_df[speed_col] >= min_log_speed)
        ]
        if not sub_df.empty:
            sub_df = sub_df[["beaufort", speed_col, "da", "timestamp", "mefcm"]]
            grouped = sub_df.groupby(["da", speed_col])
            filtered_df, time_df = DataAnalyticsHelper.filter_timestamps(
                grouped, speed_col
            )

            if not filtered_df.empty and not time_df.empty:
                aggregated = (
                    filtered_df.groupby(["beaufort", "da", speed_col])
                    .agg(
                        mefcm_sum=("mefcm", "sum"),
                        count=("mefcm", "count"),
                    )
                    .reset_index()
                )
                result_df = pd.merge(
                    aggregated,
                    time_df,
                    how="left",
                    left_on=["da", "beaufort", speed_col],
                    right_on=["da", "beaufort", speed_col],
                )

                result_df["confidence"] = result_df.apply(
                    lambda x: x["total_time"] / x["count"], axis=1
                )

                return result_df
            else:
                return pd.DataFrame()
        else:
            return sub_df

    @staticmethod
    def update_beaufort_dict(bf_dict, agg_df, speed_type):
        for _, row in agg_df.iterrows():
            beaufort, da, speed, mefcm_sum, count, total_time, seq_size = row
            da_str = str(da)
            speed_str = str(speed)
            for i in range(int(beaufort), 13):
                bf_row = bf_dict[speed_type][str(i)]
                if da_str not in bf_row:
                    bf_row[da_str] = {}
                if speed_str not in bf_row[da_str]:
                    bf_row[da_str][speed_str] = {
                        "mefcm": 0,
                        "count": 0,
                        "total_time": 0,
                    }
                bf_row[da_str][speed_str]["mefcm"] += mefcm_sum
                bf_row[da_str][speed_str]["count"] += count
                bf_row[da_str][speed_str]["total_time"] += total_time

    @staticmethod
    def calculate_spd_mefcm_correlation(
        data, log_speed_reliable, ais_data_df, weather_data_df
    ):
        if data is not None and len(data) > 0:
            bf_dict = DataAnalyticsHelper.create_bf_dict()

            if len(weather_data_df) == 0:
                return bf_dict, bf_dict
            weather_data_preprocessed = DataAnalyticsHelper.preprocess_weather_data(
                weather_data_df
            )

            doesnt_have_log_speed = False if log_speed_reliable == "log_speed" else True
            min_log_speed = 4

            measurements = pd.DataFrame(data["measurements"])
            measurements["timestamp"] = pd.to_datetime(measurements["timestamp"])
            measurements = measurements.sort_values("timestamp")

            df = DataAnalyticsHelper.process_draft_data(
                ais_data_df, measurements
            ).copy()
            df = df[df["da"].notna() & (df["da"] > 0)]
            df.loc[:, "da"] = df["da"].apply(
                lambda x: 0.5 * round(x / 0.5) if x is not None else None
            )

            df.loc[:, "beaufort"] = df["timestamp"].apply(
                lambda x: WeatherCalculations.get_beaufort(weather_data_preprocessed, x)
            )

            raw_df = df.copy()[
                df["beaufort"].notna()
                & df["mefcm"].notna()
                & (df["mefcm"] > 0)
                & df["timestamp"].notna()
            ]

            df.loc[:, "lspd"] = df["lspd"].apply(
                lambda x: 0.5 * round(x / 0.5) if x is not None else None
            )
            df.loc[:, "gspd"] = df["gspd"].apply(
                lambda x: 0.5 * round(x / 0.5) if x is not None else None
            )
            df = df.copy()[
                df["beaufort"].notna()
                & df["mefcm"].notna()
                & (df["mefcm"] > 0)
                & df["timestamp"].notna()
            ]

            if not doesnt_have_log_speed:
                lspd_agg = DataAnalyticsHelper.aggregate_filter_mefcm_cumsum(
                    df.dropna(subset=["lspd"]), "lspd", min_log_speed
                )
                DataAnalyticsHelper.update_beaufort_dict(bf_dict, lspd_agg, "lspd")
            gspd_agg = DataAnalyticsHelper.aggregate_filter_mefcm_cumsum(
                df.dropna(subset=["gspd"]), "gspd", min_log_speed
            )
            DataAnalyticsHelper.update_beaufort_dict(bf_dict, gspd_agg, "gspd")

            bf_dict = DataAnalyticsHelper.finish_bf_dict(bf_dict)

            best_fit_bf_dict = {"lspd": {}, "gspd": {}}

            gspdMin = None
            lspdMin = None

            for k, v in bf_dict["lspd"].items():
                if lspdMin is None and bf_dict["lspd"][k] != {}:
                    lspdMin = int(k)
                if gspdMin is None and bf_dict["gspd"][k] != {}:
                    gspdMin = int(k)
            bf_dict["lspdMin"] = lspdMin
            bf_dict["gspdMin"] = gspdMin

            for speed_type in bf_dict:
                if speed_type != "lspdMin" and speed_type != "gspdMin":
                    for bf in range(1, 13):
                        bf = str(bf)
                        best_fit_bf_dict[speed_type][bf] = {}
                        bf_min_x = math.inf
                        bf_max_x = 0
                        if bf_dict[speed_type][bf] != {}:
                            for draft in bf_dict[speed_type][bf]:
                                draft_speeds = bf_dict[speed_type][bf][draft]
                                if len(draft_speeds) > 1:
                                    min_x = float(list(draft_speeds.keys())[0])
                                    max_x = float(list(draft_speeds.keys())[-1])
                                    bf_min_x = min(bf_min_x, min_x)
                                    bf_max_x = max(bf_max_x, max_x)

                                # Best fit generation
                            for draft in bf_dict[speed_type][bf]:
                                draft_speeds = bf_dict[speed_type][bf][draft]
                                if len(draft_speeds) > 1:
                                    min_bf = (
                                        lspdMin if speed_type == "lspd" else gspdMin
                                    )
                                    filtered_data = raw_df[
                                        (raw_df["beaufort"] >= min_bf)
                                        & (raw_df["beaufort"] <= int(bf))
                                        & (raw_df["da"] == float(draft))
                                        & (raw_df[speed_type] <= 30)
                                        & (raw_df[speed_type] >= min_log_speed)
                                    ][["mefcm", speed_type]]
                                    filtered_data["mefcm"] = filtered_data["mefcm"] * (
                                        24 / 1000
                                    )
                                    best_fit_data = (
                                        WeatherCalculations.create_best_fit_values(
                                            filtered_data,
                                            speed_type,
                                            bf_min_x,
                                            bf_max_x,
                                        )
                                    )
                                    best_fit_bf_dict[speed_type][bf][draft] = (
                                        best_fit_data
                                    )
                                else:
                                    continue

            return bf_dict, best_fit_bf_dict

    @staticmethod
    def create_timeseries_for_frugal_status(data, vessel_id, vessel_imo, log_speed_reliable):
        grouped_list = []
        speed_type = "gspd" if log_speed_reliable == "gps_speed" else "lspd"
        speed_boundary = 4
        if vessel_imo in [9525572]:  # Key Stream
            speed_boundary = 8.5
        if data is not None and len(data) > 0:
            # prepare data and look for changes in frugalstatus and shifts over 70 seconds
            df = pd.DataFrame(data["measurements"])
            df = df[(df["sstat"] == True) & (df[speed_type] >= speed_boundary)]
            if df.empty:
                return grouped_list
            df = df[["timestamp", "fstat", "fmode"]]
            # Drop rows with any NaN values
            df = df.dropna(axis=0, how="any")

            df["timestamp"] = pd.to_datetime(df["timestamp"])
            df = df.sort_values(by="timestamp", ascending=True)

            df["time_diff"] = df["timestamp"].diff().dt.total_seconds()

            # Determine if a new group starts based on the 120 seconds threshold
            df["time_based_new_group"] = df["time_diff"] > 120

            # Check if 'fstat' changes compared to the previous row
            df["shifted_fstat"] = df["fstat"].shift()
            df["fstat_based_new_group"] = df["fstat"] != df["shifted_fstat"]

            # Determine if a new group should start based on either condition
            df["is_new_group"] = (
                df["time_based_new_group"] | df["fstat_based_new_group"]
            )

            # Assign group numbers
            df["group"] = (
                df["is_new_group"].cumsum() + 1
            )  # Adding 1 to start group numbers from 1

            # Drop helper columns and NaN columns
            df = df.drop(
                columns=[
                    "time_diff",
                    "shifted_fstat",
                    "time_based_new_group",
                    "fstat_based_new_group",
                ]
            )

            if vessel_id == 52:
                agg_dict = {
                    "frugal_mode": (
                        "fmode",
                        "first",
                    ),  # Use `frugal_mode` for vessel_id 52
                }
            else:
                agg_dict = {
                    "is_engaged": (
                        "fstat",
                        lambda x: x.iloc[0] != 0,
                    ),  # Use `is_engaged` otherwise
                }

            grouped = (
                df.groupby("group")
                .agg(
                    **agg_dict,
                    frugal_status=("fstat", lambda x: x.iloc[0]),
                    start_time=("timestamp", "first"),
                    end_time=("timestamp", "last"),
                )
                .reset_index(drop=True)
            )
            # convert to list and adjust the 'to' values
            grouped_list = []
            for i in range(len(grouped)):
                if vessel_id != 52:
                    frugal_mode = int(grouped.loc[i, "is_engaged"])
                else:
                    frugal_mode = grouped.loc[i, "frugal_mode"]
                    if frugal_mode == np.nan or frugal_mode == "NULL":
                        frugal_mode = int(grouped.loc[i, "frugal_status"])
                start_time = grouped.loc[i, "start_time"]
                end_time = grouped.loc[i, "end_time"]
                adjusted_end_time = end_time

                # check if the next 'from' is within 2 minutes
                if i < len(grouped) - 1:
                    next_start_time = grouped.loc[i + 1, "start_time"]
                    if (next_start_time - end_time).total_seconds() <= 120:
                        adjusted_end_time = next_start_time

                grouped_list.append(
                    {
                        "from": start_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "to": adjusted_end_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "frugal_mode": int(frugal_mode),
                        "total_time": WeatherCalculations.create_total_time_string(
                            start_time, adjusted_end_time
                        ),
                    }
                )
            return grouped_list

    @staticmethod
    def finish_bf_dict(bf_dict):
        final_bf_dict = DataAnalyticsHelper.create_bf_dict()
        spd_keys = ["lspd", "gspd"]
        for spd_key in spd_keys:
            for bf, bf_data in bf_dict.get(spd_key, {}).items():
                for da, da_data in bf_data.items():
                    if len(da_data) > 0:
                        spd_values = {}
                        for spd, data in sorted(
                            da_data.items(), key=lambda x: float(x[0])
                        ):
                            if data["count"] > 4:
                                fuel_consumption = round(
                                    data["mefcm"] * 24 / data["count"] / 1000, 2
                                )
                                spd_values[spd] = {
                                    "fuel_consumption": fuel_consumption,
                                    "total_time": data["total_time"],
                                }
                        if len(spd_values) > 1:
                            final_bf_dict[spd_key][bf][da] = spd_values

                if final_bf_dict[spd_key][bf] != {}:
                    sorted_bf_data = {
                        k: final_bf_dict[spd_key][bf][k]
                        for k in sorted(final_bf_dict[spd_key][bf].keys(), key=float)
                    }
                    final_bf_dict[spd_key][bf] = sorted_bf_data
        return final_bf_dict
