import logging
import traceback
from app.general_logic_helper.helpers.data.monthly_fuel_data import <PERSON><PERSON>uelDataHelper
from app.general_logic_helper.helpers.charts.donut_chart_helper import Donut<PERSON>hartHelper
from app.general_logic_helper.helpers.charts.pie_chart_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.general_logic_helper.helpers.data.summary_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.general_logic_helper.helpers.data.data_analytics_helper import DataAnalyticsHelper
from app.calculation.fuel import FuelCalculations


class DataHandler:
    def generate_monthly_fuel(self, data, _vessel_id):
        MonthlyFuelDataHelper.generate_monthly_fuel_data(data, "Tons", _vessel_id)
        MonthlyFuelDataHelper.generate_months_to_use(data, _vessel_id)

    @staticmethod
    def init_donut_helper(data, _vessel_name, log_speed_reliable, vessel_imo):
        return DonutChartHelper.init_data(data, _vessel_name, log_speed_reliable, vessel_imo)

    @staticmethod
    def init_pie_helper(redis_data, vessel_imo, log_speed_reliable):
        fuel_underway = FuelCalculations.calculate_fuel_consumption(redis_data["measurements"], vessel_imo, log_speed_reliable)
        redis_data["reports"]["fpfc"] = PieChartHelper.init_data(
            redis_data["measurements"],
            fuel_underway=fuel_underway,
            log_speed_reliable=log_speed_reliable,
            vessel_imo=vessel_imo
        )

    def init_summary_helper(self, data):
        if data is not None and len(data) > 0:
            return SummaryHelper.init_data(data)

    @staticmethod
    def init_data_analytics_helper(
        redis_data, log_speed_reliable, ais_data_df, weather_data_df, vessel_id=None, vessel_imo=None
    ):
        from_date = redis_data["measurements"][0]["timestamp"]
        to_date = redis_data["measurements"][-1]["timestamp"]
        redis_data["data_analytics"] = DataAnalyticsHelper.init_data(
            redis_data,
            from_date,
            to_date,
            log_speed_reliable,
            ais_data_df,
            weather_data_df,
            vessel_id,
            vessel_imo
        )
