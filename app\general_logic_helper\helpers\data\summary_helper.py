from datetime import datetime
from app.calculation.time import TimeCalculations


class SummaryHelper:
    @staticmethod
    async def init_data(redis_data):
        vessel_ais_data = (
            redis_data.get("latest_ais_data")
            if redis_data.get("latest_ais_data") is not None
            and len(redis_data.get("latest_ais_data")) > 0
            else None
        )
        latest_entry = (
            redis_data.get("latest_row")[0]
            if redis_data.get("latest_row") is not None
            and len(redis_data.get("latest_row")) > 0
            else None
        )
        status_temp = (
            latest_entry["frugal_status"] if latest_entry is not None else None
        )
        port_eta = None

        if status_temp == 0:
            status = "Disengaged"
        else:
            if status_temp is not None:
                status = "Engaged"
            else:
                status = "Unknown"

        if vessel_ais_data is not None and vessel_ais_data["eta_utc"] is not None:
            vessel_eta = datetime.strftime(
                TimeCalculations.validate_timestamp(vessel_ais_data["eta_utc"]), "%Y-%m-%d %H:%M:%S"
            )
            if vessel_ais_data["dest_port_name"] is not None:
                port_eta = ", " + vessel_ais_data["dest_port_name"]
            elif vessel_ais_data["destination"] is not None:
                port_eta = ", " + vessel_ais_data["destination"]
            else:
                port_eta += ", Unknown"
            if vessel_ais_data["dest_port_unlo"] is not None:
                port_eta += " (" + vessel_ais_data["dest_port_unlo"] + ")"
        else:
            vessel_eta = "Unavailable.."
            port_eta = ""

        gps_speed = latest_entry["gps_speed"] if latest_entry is not None else None

        converted_eta_to_percentage = TimeCalculations.convert_epoch_to_date_UTC(vessel_ais_data)
        percentage = 0

        if vessel_ais_data is not None and converted_eta_to_percentage is not None:
            percentage = converted_eta_to_percentage

        redis_data["summaryData"] = {
            "percentage": percentage,
            "latest_timestamp": latest_entry["timestamp"]
            if latest_entry is not None
            else None,
            "status": status,
            "vessel_eta": vessel_eta,
            "port_eta": port_eta,
            "gps_speed": gps_speed,
        }
