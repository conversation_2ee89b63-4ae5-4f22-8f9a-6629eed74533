"""
Statistics Calculation Module

Contains statistical operations and averaging calculation methods.
"""

import pandas as pd
import logging


class StatisticalCalculations:
    """Class containing statistical calculation methods."""
    
    @staticmethod
    def calculate_average_in_chunks(name, data, chunk_size):
        """
        Calculate averages in chunks of data.
        
        Args:
            name: Name of the data field to average
            data: List of data points
            chunk_size: Size of each chunk
            
        Returns:
            List of averaged values with timestamps
        """
        average = []
        chunk_sum = 0
        chunk_count = 0
        chunk_data = []

        for index, element in enumerate(data):
            if element.get(name) is not None and element.get(name) != "NULL":
                chunk_sum += float(element[name])
                chunk_count += 1
                chunk_data.append(element)

                if chunk_count == chunk_size or index == len(data) - 1:
                    if chunk_count > 0:
                        values = {}
                        result = round(chunk_sum / chunk_count, 1)
                        values[name] = result if result > 0.0 else 0.0
                        values["timestamp"] = chunk_data[len(chunk_data) - 1]["timestamp"]
                        average.append(values)

                    # Reset for next chunk
                    chunk_sum = 0
                    chunk_count = 0
                    chunk_data = []

        # Handle remaining data if any
        if chunk_count > 0:
            remaining_data = chunk_data
            remaining_values = {}
            result = round(chunk_sum / len(remaining_data), 1)
            remaining_values[name] = result if result > 0.0 else 0.0
            remaining_values["timestamp"] = remaining_data[len(remaining_data) - 1]["timestamp"]
            average.append(remaining_values)

        return average

    @staticmethod
    def calculate_averaged_columns(df, columns, chunk_size):
        """Calculate averages for multiple columns simultaneously using the same chunks."""
        result = {}

        # Filter to only include columns that exist in the dataframe
        valid_columns = [col for col in columns if col in df.columns]
        if not valid_columns:
            return result

        # Initialize with first values
        for col in valid_columns:
            first_val = float(df[col].iloc[0])
            result[col] = [round(first_val, 1) if first_val > 0 else 0]

        result["timestamp"] = [df["timestamp"].iloc[0].strftime("%Y-%m-%d %H:%M:%S")]

        half_chunk = chunk_size / 2
        total_rows = len(df)

        middle_chunk_end = total_rows - 1
        # Process middle chunks
        for i in range(1, total_rows, chunk_size):
            chunk_end = i + chunk_size
            if chunk_end >= total_rows - 1:
                break

            chunk = df.iloc[i:chunk_end]
            timestamp_location = int(i + half_chunk)
            result["timestamp"].append(
                df["timestamp"].iloc[timestamp_location].strftime("%Y-%m-%d %H:%M:%S")
            )
            for col in valid_columns:
                avg = float(chunk[col].mean())
                result[col].append(round(avg, 1) if avg > 0 else 0)
            middle_chunk_end = chunk_end

        # Process remaining data
        if total_rows > chunk_size:
            remaining = df.iloc[middle_chunk_end:]
            result["timestamp"].append(
                df["timestamp"].iloc[-1].strftime("%Y-%m-%d %H:%M:%S")
            )
            for col in valid_columns:
                avg = float(remaining[col].mean())
                result[col].append(round(avg, 1) if avg > 0 else 0)

        return result


