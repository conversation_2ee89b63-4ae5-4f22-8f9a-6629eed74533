# Standard library imports
import logging
from typing import Optional, Any

# Third-party imports
from dotenv import load_dotenv  # For loading environment variables
from orjson import orjson      # Fast JSON serialization/deserialization

# Local imports
from app.services.redis_api.redis_api_client_base import BaseRedisClient
from app.services.redis_api.redis_api_client_error import RedisApiError
from app.utils.errors import _get_exception_location
from app.utils.singleton import Singleton

# Load environment variables from .env file
load_dotenv()


class RedisApi(BaseRedisClient, metaclass=Singleton): # No need to add metaclass=Singleton here, because BaseRedisClient is already a Singleton and metaclasses are inherited, but we do it for clarity
    """
    Redis API client implementation that extends BaseRedisClient.
    Provides specific Redis operations like querying keys and retrieving time series data.
    Implements Singleton pattern to ensure only one client instance exists.
    """

    async def get_key_data(self, path: str, format: Optional[str] = None):
        """
        Retrieve data for a specific Redis key.
        
        Args:
            path: Redis key path to query
            format: Optional format specification for the response
        
        Returns:
            The value associated with the key from Redis
            
        Raises:
            RedisApiError: If the request fails
        """
        return await self.call(
            path=f"/redis/query?format={format}",
            key="results",
            method="POST",
            json={"key": path},
        )

    async def retrieve_time_series_data(self, request: Optional[Any] = None):
        """
        Retrieve time series data from Redis.
        
        Args:
            request: Query parameters for time series data (optional)
        
        Returns:
            Time series data if found, None if 404 error
            
        Raises:
            RedisApiError: For errors other than 404 Not Found
        """
        # Serialize request data to JSON string
        request_data = orjson.dumps(request).decode("utf-8")
        
        try:
            # Make API call and return parsed response
            return await self.call(
                path="timeseries",
                method="POST",
                data=request_data,
            )
        except RedisApiError as exc:
            # TODO: Remove this special handling once Fleet Efficiency is updated

            loc = _get_exception_location(exc)

            # Handle 404s gracefully
            if getattr(exc, "status_code", None) == 404:
                logging.debug(
                    "RedisApiError at %s fetching time-series for %r: Not Found",
                    loc,
                    request,
                )
                return None

            # Log other RedisApiErrors concisely, then re-raise
            logging.error(
                "RedisApiError at %s fetching time-series: %s",
                loc,
                exc.detail or str(exc),
            )
            raise
