import logging
import os
import traceback
from datetime import datetime
import pandas as pd
from openpyxl import load_workbook
import numpy as np
from io import BytesIO
from app.general_logic_helper.utils.interchange_formats import InterchangeFormats
from app.calculation.flow import FlowCalculations


class ExcelHelper:
    def __init__(self):
        self.file = None
        self.min_z = 0
        self.max_z = 0

    def get_min_max(self, path_to_file, sheet_name="Separate mode", column="Log speed"):
        if os.path.exists(path_to_file):
            with pd.ExcelFile(path_to_file) as excel_file:
                if sheet_name in excel_file.sheet_names:
                    sheet_data = excel_file.parse(sheet_name)
                    column_data = sheet_data[column]
                    self.min_z = np.min(column_data)
                    self.max_z = np.max(column_data)
                    return min, max

    @staticmethod
    def write_curve_to_sheet(
        plc_type="lyngso",
        path_to_file: str = r"c:\temp\outsheet.xlsx",
        sheet_name: str = None,
        prop_curve: list = None,
    ):
        if plc_type == "abb":
            ExcelHelper.write_abb_curve_to_sheet(
                path_to_file=path_to_file, sheet_name=sheet_name, prop_curve=prop_curve
            )
        elif plc_type == "lyngso":
            ExcelHelper.write_lyngso_curve_to_sheet(
                path_to_file=path_to_file, sheet_name=sheet_name, prop_curve=prop_curve
            )

    @staticmethod
    def write_lyngso_curve_to_sheet(
        path_to_file, sheet_name="Prop curve", prop_curve=None
    ):
        _df = pd.DataFrame(prop_curve)
        _df.columns = ["RPM", "Pitch", "Speed", "FC", "Efficiency", "Thrust level"]
        ExcelHelper._write_frame_to_new_sheet(path_to_file, sheet_name, _df)

    @staticmethod
    def write_abb_curve_to_sheet(
        path_to_file, sheet_name="Prop curve", prop_curve=None
    ):
        _df = pd.DataFrame(prop_curve)
        _df.columns = ["Thrust", "RPM", "Pitch"]
        ExcelHelper._write_frame_to_new_sheet(path_to_file, sheet_name, _df)

    @staticmethod
    def validate_flow_totals(element, key):
        # Validates if the provided key exists in the element and returns if validated
        if (
            key in element
            and element[key] is not None
            and element[key] != "NULL"
            and "boiler_1_total_6" in element[key]
        ):
            return element[key]
        return None



    @staticmethod
    def format_dict_for_excel(row, key, value):
        has_anomalies = row.pop("has_anomalies", None)
        row[key] = value
        row["has_anomalies"] = has_anomalies

    @staticmethod
    def start_boiler_net_calculation(
        vessel_data, boiler_add_groups, element, index, vessel_id
    ):
        # Instantiate necessary keys
        key = "boiler_net_fc_mass"
        ft_key = "flow_totals"
        stella_key = "boiler_1_fc_mass"
        if "flwt" in element:
            ft_key = "flwt"
            key = "boifcm"
            stella_key = "b1fcm"
        # Validates the data and converts necessary elements to correct datatype
        element_2 = vessel_data[index + 1]
        flow_totals = ExcelHelper.validate_flow_totals(element, ft_key)
        other_flow_totals = ExcelHelper.validate_flow_totals(element_2, ft_key)
        # Get the time difference between the two rows
        if not isinstance(
            element["timestamp"], (datetime, pd._libs.tslibs.timestamps.Timestamp)
        ):
            time_diff = datetime.strptime(
                element["timestamp"], "%Y-%m-%dT%H:%M:%S"
            ) - datetime.strptime(element_2["timestamp"], "%Y-%m-%dT%H:%M:%S")
        else:
            time_diff = element["timestamp"] - element_2["timestamp"]
        time_diff_seconds = time_diff.total_seconds()
        if time_diff_seconds == 0.0:
            return

        # Temporary solution for boiler calculation on Stella, because it has a different data calculation onboard.
        if vessel_id == 19:
            ExcelHelper.format_dict_for_excel(
                element, key, element[stella_key] if element[stella_key] > 0 else 0.0
            )
            return

        # Only calculate if flow_totals are not None
        if all(val is not None for val in (flow_totals, other_flow_totals)):
            # If flow_totals have 2nd boiler sensor, do this calculation
            if all(
                "boiler_2_total_6" in val for val in (flow_totals, other_flow_totals)
            ):
                first_boiler_1_total_6 = flow_totals["boiler_1_total_6"]
                first_boiler_2_total_6 = flow_totals["boiler_2_total_6"]
                first_boiler_1_total_1 = flow_totals["boiler_1_total_1"]
                first_boiler_2_total_1 = flow_totals["boiler_2_total_1"]

                second_boiler_1_total_6 = other_flow_totals["boiler_1_total_6"]
                second_boiler_2_total_6 = other_flow_totals["boiler_2_total_6"]
                second_boiler_1_total_1 = other_flow_totals["boiler_1_total_1"]
                second_boiler_2_total_1 = other_flow_totals["boiler_2_total_1"]

                # If totalizer 6 is configured correctly, do this calculation
                if (
                    all(
                        isinstance(val, float)
                        for val in (
                            first_boiler_1_total_6,
                            first_boiler_2_total_6,
                            second_boiler_1_total_6,
                            second_boiler_2_total_6,
                        )
                    )
                    and (first_boiler_1_total_6 > 0.0 or second_boiler_1_total_6 > 0.0)
                    and (first_boiler_2_total_6 > 0.0 or second_boiler_2_total_6 > 0.0)
                ):
                    flow_1 = FlowCalculations.calculate_multiple_boiler_flow(
                        time_diff_seconds,
                        first_boiler_1_total_6,
                        second_boiler_1_total_6,
                    )
                    flow_2 = FlowCalculations.calculate_multiple_boiler_flow(
                        time_diff_seconds,
                        first_boiler_2_total_6,
                        second_boiler_2_total_6,
                    )
                    if boiler_add_groups is True:
                        ExcelHelper.format_dict_for_excel(
                            element,
                            key,
                            round(abs(flow_1 + flow_2), 2)
                            if round(abs(flow_1 + flow_2), 2) > 0
                            else 0.0,
                        )
                    else:
                        ExcelHelper.format_dict_for_excel(
                            element,
                            key,
                            round(abs(flow_1 - flow_2), 2)
                            if round(abs(flow_1 - flow_2), 2) > 0
                            else 0.0,
                        )
                else:
                    # If totalizer 6 has not been configured, do this calculation
                    if all(
                        isinstance(val, float)
                        for val in (
                            first_boiler_1_total_1,
                            second_boiler_1_total_1,
                            first_boiler_2_total_1,
                            second_boiler_2_total_1,
                        )
                    ):
                        flow_1 = FlowCalculations.calculate_multiple_boiler_flow(
                            time_diff_seconds,
                            first_boiler_1_total_1,
                            second_boiler_1_total_1,
                        )
                        flow_2 = FlowCalculations.calculate_multiple_boiler_flow(
                            time_diff_seconds,
                            first_boiler_2_total_1,
                            second_boiler_2_total_1,
                        )
                        if boiler_add_groups is True:
                            ExcelHelper.format_dict_for_excel(
                                element,
                                key,
                                round(abs(flow_1 + flow_2), 2)
                                if round(abs(flow_1 + flow_2), 2) > 0
                                else 0.0,
                            )
                        else:
                            ExcelHelper.format_dict_for_excel(
                                element,
                                key,
                                round(abs(flow_1 - flow_2), 2)
                                if round(abs(flow_1 - flow_2), 2) > 0
                                else 0.0,
                            )
            else:
                # If flow_totals do not have 2nd boiler sensor, do this calculation
                if all(
                    "boiler_1_total_6" in val
                    for val in (flow_totals, other_flow_totals)
                ):
                    first_boiler_1_total_6 = flow_totals["boiler_1_total_6"]
                    first_boiler_1_total_1 = flow_totals["boiler_1_total_1"]

                    second_boiler_1_total_6 = other_flow_totals["boiler_1_total_6"]
                    second_boiler_1_total_1 = other_flow_totals["boiler_1_total_1"]
                    if all(
                        isinstance(val, float)
                        for val in (first_boiler_1_total_6, second_boiler_1_total_6)
                    ) and (
                        first_boiler_1_total_6 > 0.0 or second_boiler_1_total_6 > 0.0
                    ):
                        ExcelHelper.format_dict_for_excel(
                            element,
                            key,
                            FlowCalculations.calculate_single_single_flow(
                                time_diff_seconds,
                                first_boiler_1_total_6,
                                second_boiler_1_total_6,
                                boiler_add_groups,
                            ),
                        )
                    else:
                        if all(
                            isinstance(val, float)
                            for val in (first_boiler_1_total_1, second_boiler_1_total_1)
                        ):
                            ExcelHelper.format_dict_for_excel(
                                element,
                                key,
                                FlowCalculations.calculate_single_single_flow(
                                    time_diff_seconds,
                                    first_boiler_1_total_1,
                                    second_boiler_1_total_1,
                                    boiler_add_groups,
                                ),
                            )
        else:
            # Default 0 value if no valid flow totals are available
            ExcelHelper.format_dict_for_excel(element, key, 0.0)

    @staticmethod
    def generate_data_sheet_from_database(
        name,
        vessel_data,
        _vessel_id,
        aux_add_groups=None,
        boiler_add_groups=None,
        me_fc_add_groups=None,
        _sorted_type=None,
    ):
        if vessel_data is None or len(vessel_data) < 1:
            logging.warning("Not enough data to generate data sheet")
            return
        logging.debug("Retrieving excel data for owner: %s", name)
        vessel_data_length = len(vessel_data) - 1
        try:
            vessel_data = vessel_data.to_dict("records")
            # Calculate net main engine and aux fuel consumption
            vessel_data = InterchangeFormats.calculate_fuel_flow(vessel_data)
            logging.debug("Found vessel data to generate a sheet from: %s", name)
            for index, element in enumerate(vessel_data):
                # Do boiler net fuel consumption calculation for Excel download
                if index <= vessel_data_length:
                    if index + 1 <= vessel_data_length:
                        ExcelHelper.start_boiler_net_calculation(
                            vessel_data, boiler_add_groups, element, index, _vessel_id
                        )
                    if vessel_data[index].get(
                        "boiler_net_fc_mass"
                    ) is None or isinstance(
                        vessel_data[index]["boiler_net_fc_mass"], str
                    ):
                        ExcelHelper.format_dict_for_excel(
                            vessel_data[index], "boiler_net_fc_mass", 0
                        )

                if name == "Sigrid Theresa":
                    # ME Consumption Custom Calculation = ME Flow – (AUX Flow IN – AUX Flow OUT).
                    element["me_net_fc_mass"] = (
                        abs(element["me_1_fc_mass"] - (element["aux_net_fc_mass"]))
                        if abs(element["me_1_fc_mass"] - (element["aux_net_fc_mass"]))
                        > 0
                        else 0
                    )
            return ExcelHelper.create_excel_sheet(vessel_data, name, _sorted_type)
        except Exception as e:
            logging.error(
                "Error while generating Excel file for download via mydata: %s", e
            )
            print(traceback.format_exc())

    @staticmethod
    def create_excel_sheet(vessel_data, name, _sorted_type=None):
        _df = pd.DataFrame(vessel_data)
        if _sorted_type is True:
            if "timestamp" in _df.columns:
                first_columns = ["timestamp"]
                other_columns = [col for col in _df.columns if col != "timestamp"]
                sorted_columns = first_columns + sorted(
                    other_columns
                )  # Sort remaining columns
                _df = _df[sorted_columns]
        with BytesIO() as output:
            with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
                _df.to_excel(excel_writer=writer, sheet_name=name)
                for column in _df:
                    item = _df[column].astype(str).map(len).max()
                    column_name = str(_df[column].name)
                    len_column_name = len(column_name)
                    column_length = max(
                        (
                            item,  # len of largest item
                            len_column_name,  # len of column name/header
                        )
                    )
                    if column_length == len_column_name:
                        column_length += 2  # adding a little extra space
                    else:
                        column_length += 1  # adding a little extra space

                    col_idx = (
                        _df.columns.get_loc(column) + 1
                    )  # We want to change the column ID by one.

                    writer.sheets[name].set_column(col_idx, col_idx, column_length)
                writer.sheets[name].freeze_panes(1, 0)
            output.flush()
            return output.getvalue()

    @staticmethod
    def generate_data_sheet_for_wallboard(
        vessel_data: dict = None, vessel_id=None, vessel_name=None
    ):
        if vessel_data is None or len(vessel_data) < 1:
            logging.warning("Not enough data to generate data sheet")
            return
        try:
            observations = vessel_data
            _iter = iter(observations)
            current_vessel = vessel_name

            if current_vessel is None:
                logging.warning(
                    "Somehow there is no data to make an Excel sheet from for vessel with ID %s",
                    vessel_id,
                )
                return None

            _df = pd.DataFrame(observations)
            with BytesIO() as output:
                with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
                    _df.to_excel(writer, current_vessel, engine="xlsxwriter")
                    for idx, col in enumerate(_df):  # loop through all columns
                        series = _df[col]
                        max_len = (
                            max(
                                (
                                    series.astype(str)
                                    .map(len)
                                    .max(),  # len of largest item
                                    len(str(series.name)),  # len of column name/header
                                )
                            )
                            + 1
                        )  # adding a little extra space
                        writer.sheets[current_vessel].set_column(
                            idx + 1, idx + 1, max_len
                        )
                output.flush()
                return output.getvalue()
        except Exception as e:
            logging.error(
                "Error while generating Excel file for download via mydata: %s", e
            )

    @staticmethod
    def write_serial_to_sheet(path_to_file, serial=1):
        _df2 = pd.DataFrame([("Serial", serial)])
        _df2.columns = ["", "Value"]
        ExcelHelper._write_frame_to_new_sheet(path_to_file, "Metadata", _df2)

    @staticmethod
    def load_response_curve_from_sheet(
        path_to_file="c:/temp/frugal-propulsion-plc-tables.xlsx",
    ):
        try:
            _df = pd.read_excel(path_to_file, sheet_name="Prop curve")
            return _df.get_values()
        except Exception as e:
            logging.error("Cannot load data from sheet at %s: %s", path_to_file, e)
            raise e

    """Appends a sheet to an existing workbook at path_to_file or creates a new one if none is present"""

    @staticmethod
    def _write_frame_to_new_sheet(
        path_to_file=None, sheet_name="sheet", data_frame=None
    ):
        book = None
        # noinspection PyBroadException
        try:
            book = load_workbook(path_to_file)
        except:
            logging.debug("Creating new workbook at %s", path_to_file)
        with pd.ExcelWriter(path_to_file, engine="openpyxl") as writer:
            if book is not None:
                writer.book = book
            data_frame.to_excel(writer, sheet_name, index=False)


"""Remember: the excel helper has two parts now. One for generating the sheet - in the cloud repo - and one for
loading it into the PLC the onboard repo. Good to know."""
if __name__ == "__main__":
    loader = ExcelHelper()
