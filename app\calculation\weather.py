"""
Weather and Analytics Calculation Module

Contains weather-related calculations, Beaufort scale operations, and data analytics
mathematical functions extracted from DataAnalyticsHelper.
"""

import bisect
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from app.calculation.statistics import StatisticalCalculations


class WeatherCalculations:
    """Class containing weather and analytics calculation methods."""
    
    # Beaufort Wind Scale for weather calculations
    BEAUFORT_SCALE = [
        {"category": "Calm", "min_speed": 0, "max_speed": 0.2, "beaufort number": 0},
        {
            "category": "Light air",
            "min_speed": 0.3,
            "max_speed": 1.5,
            "beaufort number": 1,
        },
        {
            "category": "Light breeze",
            "min_speed": 1.6,
            "max_speed": 3.3,
            "beaufort number": 2,
        },
        {
            "category": "Gentle breeze",
            "min_speed": 3.4,
            "max_speed": 5.4,
            "beaufort number": 3,
        },
        {
            "category": "Moderate breeze",
            "min_speed": 5.5,
            "max_speed": 7.9,
            "beaufort number": 4,
        },
        {
            "category": "Fresh breeze",
            "min_speed": 8.0,
            "max_speed": 10.7,
            "beaufort number": 5,
        },
        {
            "category": "Strong breeze",
            "min_speed": 10.8,
            "max_speed": 13.8,
            "beaufort number": 6,
        },
        {
            "category": "Near gale",
            "min_speed": 13.9,
            "max_speed": 17.1,
            "beaufort number": 7,
        },
        {
            "category": "Gale",
            "min_speed": 17.2,
            "max_speed": 20.7,
            "beaufort number": 8,
        },
        {
            "category": "Strong gale",
            "min_speed": 20.8,
            "max_speed": 24.4,
            "beaufort number": 9,
        },
        {
            "category": "Storm",
            "min_speed": 24.5,
            "max_speed": 28.4,
            "beaufort number": 10,
        },
        {
            "category": "Violent storm",
            "min_speed": 28.5,
            "max_speed": 32.6,
            "beaufort number": 11,
        },
    ]

    @staticmethod
    def calculate_beaufort(wind_speed):
        """
        Calculate Beaufort number from wind speed.
        
        Args:
            wind_speed: Wind speed value
            
        Returns:
            Beaufort number (0-12)
        """
        rounded_wind_speed = round(wind_speed, 1)
        if rounded_wind_speed >= 32.7:
            return 12  # Hurricane level
        for beaufort in WeatherCalculations.BEAUFORT_SCALE:
            if beaufort["min_speed"] <= rounded_wind_speed <= beaufort["max_speed"]:
                return beaufort["beaufort number"]

    @staticmethod
    def get_beaufort(weather_data_preprocessed, target_timestamp):
        """
        Find the Beaufort number for a given timestamp.
        
        Args:
            weather_data_preprocessed: List of (timestamp, beaufort) tuples
            target_timestamp: Target timestamp to find Beaufort number for
            
        Returns:
            Beaufort number for the closest timestamp
        """
        # Convert the target timestamp to a pandas Timestamp object
        target_timestamp = pd.to_datetime(target_timestamp)

        # Use bisect to find the closest timestamp
        index = bisect.bisect(weather_data_preprocessed, (target_timestamp,))

        # Get the Beaufort number for the closest timestamp
        _, beaufort = weather_data_preprocessed[
            min(index, len(weather_data_preprocessed) - 1)
        ]

        return beaufort

    @staticmethod
    def calculate_total_time(group, speed_col):
        """
        Calculate total time for grouped data with Beaufort grouping.
        
        Args:
            group: Pandas DataFrame group
            speed_col: Speed column name
            
        Returns:
            DataFrame with total time calculations
        """
        group["beaufort_group"] = (
            (group["beaufort"] != group["beaufort"].shift())
            | (group["sequence_id"] != group["sequence_id"].shift())
        ).cumsum()

        total_time = (
            group.groupby(["sequence_id", "beaufort", "beaufort_group"])
            .apply(
                lambda g: pd.Series(
                    {
                        "total_time": (
                            g["timestamp"].max() - g["timestamp"].min()
                        ).total_seconds()
                        + 60,
                        speed_col: g[speed_col].iloc[0],
                        "da": g["da"].iloc[0],
                    }
                )
            )
            .reset_index()
        )

        combined_total_time = (
            total_time.groupby(["beaufort", speed_col, "da"])
            .agg({"total_time": "sum"})
            .reset_index()
        )

        return combined_total_time

    @staticmethod
    def create_total_time_string(from_time, to_time):
        """
        Create a human-readable time duration string.
        
        Args:
            from_time: Start datetime
            to_time: End datetime
            
        Returns:
            Formatted time duration string
        """
        # Calculate the time difference
        delta = to_time - from_time

        # Get the total number of seconds
        total_seconds = int(delta.total_seconds())

        # Calculate the intervals
        days, remainder = divmod(total_seconds, 86400)  # 86400 seconds in a day
        hours, remainder = divmod(remainder, 3600)  # 3600 seconds in an hour
        minutes, seconds = divmod(remainder, 60)  # 60 seconds in a minute

        # Format the result based on the largest non-zero interval
        if days > 0:
            return f"{days} days, {hours} hours, {minutes} minutes, {seconds} seconds"
        elif hours > 0:
            return f"{hours} hours, {minutes} minutes, {seconds} seconds"
        elif minutes > 0:
            return f"{minutes} minutes, {seconds} seconds"
        else:
            return f"{seconds} seconds"

    @staticmethod
    def create_best_fit_values(filtered_data, speed_type, min_x, max_x):
        """
        Create exponential best fit values for speed vs fuel consumption data.
        
        Args:
            filtered_data: DataFrame with speed and fuel consumption data
            speed_type: Speed column name
            min_x: Minimum x value for fit
            max_x: Maximum x value for fit
            
        Returns:
            Dictionary of best fit line values
        """
        if not filtered_data.empty:
            x = filtered_data[speed_type].to_numpy()
            y = filtered_data["mefcm"].to_numpy()

            log_y = np.log(y)

            degree = 1
            coefficients = np.polyfit(x, log_y, degree)

            # Convert the linear fit to an exponential model
            a = np.exp(coefficients[1])  # a = exp(intercept)
            b = coefficients[0]  # b = slope

            # Define the exponential function
            def exponential_func(x):
                return a * np.exp(b * x)

            # Generate new x values
            new_x = np.arange(min_x, max_x + 0.5, 0.5)

            # Calculate the exponential fit for the new x values
            y_fit = exponential_func(new_x)

            best_fit_line = {
                str(float(k)): float(round(y_fit[i], 2)) for i, k in enumerate(new_x)
            }

            return best_fit_line
        else:
            return {}


