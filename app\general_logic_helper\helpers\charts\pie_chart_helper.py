import pandas as pd
from app.calculation.time import TimeCalculations
from app.calculation.charts import ChartCalculations


class PieChartHelper:

    @staticmethod
    def init_data(
        vessel_data,
        log_speed_reliable,
        fuel_underway=None,
        is_donut_chart=False,
        vessel_name=None,
        vessel_imo=None
    ):
        if is_donut_chart or (fuel_underway and len(fuel_underway) > 0):
            speed_type = "gspd" if log_speed_reliable == "gps_speed" else "lspd"
            speed_boundary = 4
            if vessel_imo in [9525572]:  # Key Stream
                speed_boundary = 8.5

            # Vessel has to be sailing
            df = pd.DataFrame(vessel_data) if not is_donut_chart else vessel_data

            speed_df = ChartCalculations.filter_sailing_data(df, speed_type, speed_boundary, vessel_imo)

            df_frugal_on, df_frugal_off = ChartCalculations.separate_frugal_data(speed_df)

            if not is_donut_chart:
                on_propulsion_fc = fuel_underway[0]
                off_propulsion_fc = fuel_underway[1]

                on_propulsion = 0
                off_propulsion = 0
                avg_speed_fp_on = ChartCalculations.calculate_average_speed(df_frugal_on, speed_type)
                avg_speed_fp_off = ChartCalculations.calculate_average_speed(df_frugal_off, speed_type)

            seconds_total = ChartCalculations.calculate_time_counts(speed_df)
            seconds_on = ChartCalculations.calculate_time_counts(df_frugal_on)
            seconds_off = ChartCalculations.calculate_time_counts(df_frugal_off)

            hours, min = TimeCalculations.seconds_to_hours_minutes(seconds_total)
            on_hours, on_min = TimeCalculations.seconds_to_hours_minutes(seconds_on)
            off_hours, off_min = TimeCalculations.seconds_to_hours_minutes(seconds_off)

            if is_donut_chart:
                if seconds_total > 0:
                    on_percentage, off_percentage = ChartCalculations.calculate_time_percentages(
                        seconds_on, seconds_off, seconds_total
                    )
                    return {
                        "on_percentage": on_percentage,
                        "off_percentage": off_percentage,
                        "frugal_usage": "Past 7 Days (Engine Underway)",
                        "total_hours": [hours, min],
                        "on_hours": [on_hours, on_min],
                        "off_hours": [off_hours, off_min],
                    }
                else:
                    return {
                        "frugal_usage": f"{vessel_name} has not sailed the past 7 days.",
                    }

            else:
                fc_total = on_propulsion_fc + off_propulsion_fc
                if fc_total > 0:
                    if fuel_underway is not None and len(fuel_underway) > 0:
                        on_propulsion = fuel_underway[0]
                        off_propulsion = fuel_underway[1]

                if seconds_total > 0:
                    on_percentage, off_percentage = ChartCalculations.calculate_time_percentages(
                        seconds_on, seconds_off, seconds_total
                    )

                    return {
                        "on_percent_fc": on_percentage,
                        "off_percent_fc": off_percentage,
                        "total_hours": [hours, min],
                        "on_hours": [on_hours, on_min],
                        "off_hours": [off_hours, off_min],
                        "on_propulsion_fc": on_propulsion,
                        "off_propulsion_fc": off_propulsion,
                        "avg_speed_on": round(avg_speed_fp_on, 2),
                        "avg_speed_off": round(avg_speed_fp_off, 2),
                    }
                else:
                    return {
                        "on_percent_fc": 0,
                        "off_percent_fc": 100,
                        "total_hours": [0, 0],
                        "on_hours": [0, 0],
                        "off_hours": [0, 0],
                        "on_propulsion_fc": 0,
                        "off_propulsion_fc": 0,
                        "avg_speed_on": 0,
                        "avg_speed_off": 0,
                    }
