import logging
import async<PERSON>
import j<PERSON>
from typing import Any, Optional, Type
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from httpx import ConnectError, TimeoutException
from app.services.redis_api.redis_api_client_error import RedisApiError
from pydantic import BaseModel
from enum import Enum


class ErrorStatus(str, Enum):
    ERROR = "error"


class ErrorResponse(BaseModel):
    status: ErrorStatus = ErrorStatus.ERROR
    message: str
    errors: Optional[Any] = None
    data: None = None


def _get_exception_location(exc: Exception) -> str:
    """
    Get the file:line where the exception was raised.
    """
    tb = exc.__traceback__
    while tb and tb.tb_next:
        tb = tb.tb_next
    if tb:
        frame = tb.tb_frame
        return f"{frame.f_code.co_filename}:{tb.tb_lineno}"
    return "<unknown>"


def _make_handler(
    exc_class: Type[Exception],
    status_code: int,
    default_message: str,
    log_level: int = logging.WARNING,
    include_errors: bool = False,
    use_exc_detail: bool = True
):
    """
    Return an exception handler that logs location and message only.
    """
    async def handler(request: Request, exc: exc_class):
        message = getattr(exc, 'detail', None) if use_exc_detail and hasattr(exc, 'detail') else default_message
        errors = exc.errors() if include_errors and hasattr(exc, 'errors') else None
        loc = _get_exception_location(exc)
        logging.log(log_level, f"{exc_class.__name__} at {loc} {request.method} {request.url.path}: {message}")
        return JSONResponse(
            status_code=status_code,
            content=ErrorResponse(message=message or default_message, errors=errors).dict()
        )
    return handler


def register_error_handlers(app: FastAPI) -> None:
    # TODO add more if needed

    """
    Register concise exception handlers on FastAPI app.
    Call after `app = FastAPI(debug=False)` and before routers.
    """
    # HTTP errors
    app.add_exception_handler(
        StarletteHTTPException,
        _make_handler(StarletteHTTPException, 404, "Not Found")
    )
    app.add_exception_handler(
        HTTPException,
        _make_handler(HTTPException, 400, "Bad Request")
    )
    # Validation
    app.add_exception_handler(
        RequestValidationError,
        _make_handler(RequestValidationError, 422, "Validation error", log_level=logging.INFO, include_errors=True, use_exc_detail=False)
    )
    # Redis
    app.add_exception_handler(
        RedisApiError,
        _make_handler(RedisApiError, 502, "Redis service error", log_level=logging.ERROR)
    )
    # Network & data
    for exc_cls, code, msg, lvl in [
        (ConnectError, 503, "Service unavailable", logging.ERROR),
        (TimeoutException, 504, "Request timeout", logging.WARNING),
        (asyncio.TimeoutError, 504, "Operation timeout", logging.WARNING),
        (json.JSONDecodeError, 400, "Invalid JSON format", logging.WARNING),
        (ValueError, 400, "Invalid data provided", logging.WARNING),
        (AttributeError, 400, "Invalid data access", logging.WARNING),
    ]:
        app.add_exception_handler(exc_cls, _make_handler(exc_cls, code, msg, log_level=lvl))
    # Catch-all
    app.add_exception_handler(
        Exception,
        _make_handler(Exception, 500, "Internal server error", log_level=logging.ERROR)
    )


def register_exception_middleware(app: FastAPI) -> None:
    """
    Use FastAPI's decorator to catch unhandled exceptions at middleware level.
    """
    @app.middleware("http")
    async def catch_exceptions(request: Request, call_next):
        try:
            return await call_next(request)
        except Exception as exc:
            loc = _get_exception_location(exc)
            logging.error(f"Unhandled exception at {loc} {request.method} {request.url.path}: {str(exc)}")
            return JSONResponse(
                status_code=500,
                content=ErrorResponse(message="Internal server error").dict()
            )
