"""
Flow Calculation Module

Contains flow and mass calculation methods.
"""

import logging


class FlowCalculations:
    """Class containing flow-related calculation methods."""
    
    @staticmethod
    def calculate_single_single_flow(time_diff, boiler_1, boiler_2, boiler_add_groups):
        """
        Calculate single boiler flow.
        
        Args:
            time_diff: Time difference
            boiler_1: First boiler value
            boiler_2: Second boiler value
            boiler_add_groups: Whether to add or subtract boiler values
            
        Returns:
            Calculated flow value
        """
        if boiler_add_groups is True:
            flow = abs((boiler_1 + boiler_2)) / time_diff
        else:
            flow = abs((boiler_1 - boiler_2)) / time_diff
        result = round((flow * 10**3) * 3600, 2)
        if result < 0:
            result = 0.0
        return result

    @staticmethod
    def calculate_multiple_boiler_flow(time_diff, boiler_1, boiler_2):
        """
        Calculate multiple boiler flow.
        
        Args:
            time_diff: Time difference
            boiler_1: First boiler value
            boiler_2: Second boiler value
            
        Returns:
            Calculated flow value
        """
        flow = abs((boiler_1 - boiler_2)) / time_diff
        result = (flow * 10**3) * 3600
        if result < 0:
            result = 0.0
        return result


