"""
Chart Calculation Module

Contains chart-related mathematical operations and calculations.
"""

import pandas as pd
from datetime import datetime, timedelta


class ChartCalculations:
    """Class containing chart-related calculation methods."""

    @staticmethod
    def calculate_time_percentages(seconds_on, seconds_off, seconds_total):
        """
        Calculate time-based percentages for charts.
        
        Args:
            seconds_on: Seconds when system is on
            seconds_off: Seconds when system is off
            seconds_total: Total seconds
            
        Returns:
            Tuple of (on_percentage, off_percentage)
        """
        if seconds_total > 0:
            on_percentage = round(seconds_on / seconds_total * 100, 1)
            off_percentage = round(seconds_off / seconds_total * 100, 1)
            return on_percentage, off_percentage
        return 0, 100

    @staticmethod
    def filter_sailing_data(df, speed_column, speed_boundary=4, vessel_imo=None):
        """
        Filter data for sailing vessels based on speed criteria.
        
        Args:
            df: Pandas DataFrame
            speed_column: Name of speed column
            speed_boundary: Minimum speed threshold
            vessel_imo: Vessel IMO number for special cases
            
        Returns:
            Filtered DataFrame
        """
        # Adjust speed boundary for specific vessels
        if vessel_imo in [9525572]:  # Key Stream
            speed_boundary = 8.5

        return df[(df["sstat"] == True) & (df[speed_column] >= speed_boundary)]

    @staticmethod
    def separate_frugal_data(df):
        """
        Separate data based on frugal status.

        Args:
            df: Pandas DataFrame

        Returns:
            Tuple of (frugal_on_df, frugal_off_df)
        """
        df_frugal_on = df[(df["fstat"] >= 1)]
        df_frugal_off = df[(df["fstat"] < 1)]
        return df_frugal_on, df_frugal_off

    @staticmethod
    def calculate_average_speed(df, speed_type):
        """
        Calculate average speed from DataFrame.

        Args:
            df: Pandas DataFrame
            speed_type: Speed column name (e.g., 'lspd', 'gspd')

        Returns:
            Average speed as float, 0 if DataFrame is empty
        """
        if df.size > 0:
            return float(df[speed_type].mean())
        return 0

    @staticmethod
    def calculate_time_counts(df, time_multiplier=60):
        """
        Calculate time counts from DataFrame timestamp column.

        Args:
            df: Pandas DataFrame with timestamp column
            time_multiplier: Multiplier for time calculation (default 60 for minutes to seconds)

        Returns:
            Total time in seconds as integer
        """
        return int(df["timestamp"].count() * time_multiplier)
